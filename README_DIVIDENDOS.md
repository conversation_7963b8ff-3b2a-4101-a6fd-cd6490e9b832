# 💎 SISTEMA DE DIVIDENDOS - Integração com Dashboard

Este documento explica como usar o sistema de dividendos integrado ao dashboard de análise temporal da carteira.

## 📋 Como Funciona

O sistema de dividendos foi integrado ao arquivo `src/analise_carteira_temporal.py` e ao dashboard Streamlit. Os dividendos são automaticamente incluídos no cálculo do capital total e rendimento da carteira.

## 📁 Arquivo de Dividendos

### Estrutura do arquivo `dividendos.csv`

```csv
ticker,data,valor
TIMS3.SA,2025-07-23,61.97
PETR4.SA,2025-07-15,25.50
WEGE3.SA,2025-07-10,18.75
```

### Campos obrigatórios:
- **ticker**: <PERSON><PERSON><PERSON> da a<PERSON> (formato: XXXX3.SA, XXXX4.SA, etc.)
- **data**: Data do recebimento do dividendo (formato: YYYY-MM-DD)
- **valor**: Valor do dividendo recebido em reais

## 🚀 Como Usar

### 1. Adicionar Dividendos
Edite o arquivo `dividendos.csv` na raiz do projeto e adicione os dividendos recebidos:

```csv
ticker,data,valor
PETR4.SA,2025-07-15,25.50
WEGE3.SA,2025-07-10,18.75
VALE3.SA,2025-07-20,32.10
```

### 2. Executar Análise Temporal
Execute a análise temporal para atualizar os dados:

```bash
uv run python src/analise_carteira_temporal.py
```

### 3. Visualizar no Dashboard
Execute o dashboard para ver os dividendos integrados:

```bash
uv run streamlit run dashboard_carteira.py
```

## 📊 O que é Calculado

### Métricas Incluídas:
1. **Dividendos Recebidos**: Total acumulado de dividendos até cada data
2. **Capital Total**: Agora inclui dividendos no cálculo
3. **Rendimento**: Considera dividendos como parte do retorno total

### Fórmulas Atualizadas:
```
Capital Disponível = Capital Inicial - Valor Investido Bruto + Valor Vendido + Dividendos
Capital Total = Capital Disponível + Valor Atual das Ações
Rendimento = Capital Total - Capital Inicial
```

## 📈 Visualizações

### Dashboard Streamlit:
- **Métrica "💎 DIVIDENDS"**: Mostra total de dividendos recebidos
- **Gráfico Principal**: Linha roxa mostra evolução dos dividendos
- **Tabela de Dados**: Coluna "DIVIDENDS_RECEIVED" com valores acumulados

### Gráficos Matplotlib:
- **Linha dos Dividendos**: Aparece automaticamente se houver dividendos > 0
- **Área Preenchida**: Considera dividendos no cálculo do rendimento total

## 🔧 Funcionalidades Técnicas

### Carregamento Automático:
- O sistema verifica automaticamente se existe o arquivo `dividendos.csv`
- Se não existir, continua funcionando normalmente sem dividendos
- Mensagens informativas sobre o carregamento dos dividendos

### Cálculo Temporal:
- Dividendos são acumulados até cada data de análise
- Apenas dividendos recebidos até a data atual são considerados
- Integração perfeita com a lógica existente de análise temporal

### Tratamento de Erros:
- Arquivo não encontrado: Continua sem dividendos
- Erros de formato: Mensagem de aviso e continua sem dividendos
- Datas inválidas: Ignoradas automaticamente

## 📝 Exemplo Prático

### Cenário:
- Capital inicial: R$ 1.000,00
- Valor atual das ações: R$ 850,00
- Dividendos recebidos: R$ 106,22
- Capital disponível: R$ 218,37

### Resultado:
- **Sem dividendos**: Capital Total = R$ 1.068,37 (6,84% de rendimento)
- **Com dividendos**: Capital Total = R$ 1.174,59 (17,46% de rendimento)

## 🎯 Benefícios

1. **Análise Completa**: Considera todos os retornos (valorização + dividendos)
2. **Histórico Temporal**: Mostra evolução dos dividendos ao longo do tempo
3. **Integração Automática**: Funciona com todos os gráficos e métricas existentes
4. **Flexibilidade**: Fácil adição de novos dividendos
5. **Compatibilidade**: Mantém funcionamento normal mesmo sem dividendos

## 📋 Checklist de Uso

- [ ] Criar/atualizar arquivo `dividendos.csv`
- [ ] Verificar formato das datas (YYYY-MM-DD)
- [ ] Confirmar códigos dos tickers (formato .SA)
- [ ] Executar análise temporal
- [ ] Verificar dashboard atualizado
- [ ] Confirmar métricas de dividendos

---

## 🔍 Troubleshooting

### Problema: Dividendos não aparecem
**Solução**: Verificar se o arquivo `dividendos.csv` está na raiz do projeto

### Problema: Erro de formato
**Solução**: Verificar se as datas estão no formato YYYY-MM-DD

### Problema: Valores não batem
**Solução**: Confirmar se os tickers estão corretos (incluir .SA)

---

**💡 Dica**: Os dividendos são considerados como "dinheiro em caixa" adicional, aumentando o capital disponível e o capital total da carteira.
