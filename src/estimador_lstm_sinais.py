#!/usr/bin/env python3
"""
Script para estimativa de sinais de compra e venda usando LSTM
Baseado nas mesmas features do estimador XGBoost, importando de features_xgboost.py
Utiliza arquitetura LSTM para previsão sequencial de sinais binários (0=Venda, 1=Compra)
"""

import os
import sys
import random
import warnings
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.callbacks import EarlyStopping

# Fixar seeds para reprodutibilidade
RANDOM_SEED = 42
np.random.seed(RANDOM_SEED)
random.seed(RANDOM_SEED)
tf.random.set_seed(RANDOM_SEED)
os.environ['PYTHONHASHSEED'] = str(RANDOM_SEED)

# ============================================================================
# FUNÇÕES DE CACHE DE MODELOS LSTM
# ============================================================================

def verificar_modelo_lstm_treinado_hoje(modelo_dir, modelo_nome):
    """
    Verifica se o modelo LSTM já foi treinado hoje
    """
    modelo_path = os.path.join(modelo_dir, modelo_nome)
    if not os.path.exists(modelo_path):
        return False, None

    # Verificar data de modificação do arquivo
    timestamp_arquivo = os.path.getmtime(modelo_path)
    data_arquivo = datetime.fromtimestamp(timestamp_arquivo).date()
    data_hoje = datetime.now().date()

    if data_arquivo == data_hoje:
        print(f"✅ Modelo LSTM {modelo_nome} já foi treinado hoje ({data_hoje})")
        return True, modelo_path
    else:
        print(f"⏰ Modelo LSTM {modelo_nome} foi treinado em {data_arquivo}, retreinando...")
        return False, None

def carregar_modelo_lstm_existente(modelo_path, scaler_features_path, scaler_target_path):
    """
    Carrega um modelo LSTM existente do disco
    """
    try:
        # Carregar modelo LSTM
        from tensorflow.keras.models import load_model
        model = load_model(modelo_path)

        # Carregar scaler das features
        import pickle
        with open(scaler_features_path, 'rb') as f:
            scaler_features = pickle.load(f)

        # Carregar scaler do target
        with open(scaler_target_path, 'rb') as f:
            scaler_target = pickle.load(f)

        print(f"📦 Modelo LSTM carregado com sucesso:")
        print(f"   • Arquitetura: {len(model.layers)} camadas")
        print(f"   • Parâmetros: {model.count_params():,}")

        return model, scaler_features, scaler_target
    except Exception as e:
        print(f"❌ Erro ao carregar modelo LSTM: {e}")
        return None, None, None

def salvar_modelo_lstm(model, scaler_features, scaler_target, feature_cols, metricas):
    """
    Salva o modelo LSTM treinado
    """
    modelo_dir = 'results/models/lstm_analysis'
    os.makedirs(modelo_dir, exist_ok=True)

    # Salvar modelo LSTM
    modelo_path = os.path.join(modelo_dir, 'modelo_lstm.h5')
    model.save(modelo_path)

    # Salvar scaler das features
    scaler_features_path = os.path.join(modelo_dir, 'scaler_features_lstm.pkl')
    import pickle
    with open(scaler_features_path, 'wb') as f:
        pickle.dump(scaler_features, f)

    # Salvar scaler do target
    scaler_target_path = os.path.join(modelo_dir, 'scaler_target_lstm.pkl')
    with open(scaler_target_path, 'wb') as f:
        pickle.dump(scaler_target, f)

    # Salvar metadados
    metadata_path = os.path.join(modelo_dir, 'metadata_lstm.pkl')
    metadata = {
        'feature_cols': feature_cols,
        'rmse': metricas['rmse'],
        'mae': metricas['mae'],
        'r2': metricas['r2'],
        'data_treinamento': datetime.now().isoformat()
    }

    with open(metadata_path, 'wb') as f:
        pickle.dump(metadata, f)

    print(f"💾 Modelo LSTM salvo em: {modelo_dir}/")
    print(f"   • Modelo: modelo_lstm.h5")
    print(f"   • Scaler: scaler_lstm.pkl")
    print(f"   • Metadata: metadata_lstm.pkl")

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment
from features_xgboost import calcular_features_e_sinais

# Importar funções do classificador XGBoost para reutilizar lógica de cache
from classificador_xgboost_sinais import (
    carregar_acoes_diversificadas,
    baixar_dados_acao_otimizado
)
warnings.filterwarnings('ignore')
# Usar a função do classificador XGBoost para manter consistência

def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo CSV
    Retorna um dicionário com ticker: quantidade
    """
    try:
        file_paths = config.get_file_paths()
        carteira_path = file_paths['carteira']

        if not os.path.exists(carteira_path):
            print(f"   ⚠️ Arquivo de carteira não encontrado: {carteira_path}")
            return {}

        df_carteira = pd.read_csv(carteira_path)
        carteira = {}

        for _, row in df_carteira.iterrows():
            ticker = str(row['ticker']).strip()
            quantidade = int(row['quantidade'])

            # Apenas posições positivas (ações em carteira)
            if quantidade > 0:
                # Adicionar .SA se necessário
                if not ticker.endswith('.SA'):
                    ticker_sa = ticker + '.SA'
                else:
                    ticker_sa = ticker
                carteira[ticker_sa] = quantidade

        print(f"📋 Carteira atual carregada: {len(carteira)} posições ativas")
        for ticker, qtd in carteira.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira

    except Exception as e:
        print(f"   ❌ Erro ao carregar carteira: {e}")
        return {}


def preparar_dados_lstm(dataset, feature_cols, target_col, sequence_length=20):
    """
    Prepara os dados para entrada no LSTM (sequências) para regressão
    Target: variação percentual da ação no dia seguinte (Target_PctChange)
    """
    X = dataset[feature_cols]
    y = dataset[target_col]  # Target_PctChange para predição de variação percentual

    X_seq, y_seq = [], []
    for i in range(len(X) - sequence_length):
        X_seq.append(X.iloc[i:(i + sequence_length)].values)
        y_seq.append(y.iloc[i + sequence_length])  # Variação percentual do dia seguinte
    return np.array(X_seq), np.array(y_seq)


def treinar_lstm_regressao(X_train, y_train, X_test, y_test, input_shape, epochs=50, batch_size=32):
    """
    Treina um modelo LSTM para regressão (predição de variações percentuais)
    """
    model = Sequential()
    model.add(LSTM(config.get('lstm.units', 50), input_shape=input_shape, return_sequences=False))
    model.add(Dropout(config.get('lstm.dropout', 0.2)))
    model.add(Dense(config.get('lstm.dense_units', 25), activation='relu'))
    model.add(Dense(1))  # Output layer for regression (no activation)

    model.compile(optimizer='adam', loss='mean_squared_error', metrics=['mae'])

    early_stop = EarlyStopping(monitor='val_loss', patience=config.get('lstm.patience', 10), restore_best_weights=True)

    history = model.fit(
        X_train, y_train,
        validation_data=(X_test, y_test),
        epochs=epochs,
        batch_size=batch_size,
        callbacks=[early_stop],
        verbose=1
    )

    return model, history





def carregar_carteira_atual():
    """
    Carrega a carteira atual do arquivo carteira.csv
    Retorna um dicionário com ticker -> quantidade atual
    """
    try:
        carteira_df = pd.read_csv('carteira.csv')

        # Calcular posição atual de cada ticker
        carteira_atual = {}
        for _, row in carteira_df.iterrows():
            ticker = row['ticker']
            quantidade = row['quantidade']

            if ticker in carteira_atual:
                carteira_atual[ticker] += quantidade
            else:
                carteira_atual[ticker] = quantidade

        # Filtrar apenas tickers com quantidade > 0
        carteira_atual = {ticker: qtd for ticker, qtd in carteira_atual.items() if qtd > 0}

        print(f"📋 Carteira atual carregada: {len(carteira_atual)} posições ativas")
        for ticker, qtd in carteira_atual.items():
            ticker_clean = ticker.replace('.SA', '')
            print(f"   • {ticker_clean}: {qtd} ações")

        return carteira_atual

    except FileNotFoundError:
        print(f"⚠️ Arquivo carteira.csv não encontrado - mostrando todos os sinais de venda")
        return {}
    except Exception as e:
        print(f"⚠️ Erro ao carregar carteira: {e} - mostrando todos os sinais de venda")
        return {}






def aplicar_predicoes_lstm(acoes_dados, model, scaler_features, scaler_target, feature_cols, sequence_length):
    """
    Aplica as predições do modelo LSTM aos dados de cada ação
    Gera sinais de compra/venda baseados na comparação entre preço atual e preço previsto
    """
    threshold = config.get('xgboost.features.pct_threshold', 0.4)  # Threshold para variação percentual
    acoes_com_predicoes = {}

    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > sequence_length:
            dados_copy = dados.copy()

            # Verificar se todas as features existem
            features_disponiveis = [col for col in feature_cols if col in dados_copy.columns]
            if len(features_disponiveis) != len(feature_cols):
                print(f"     ⚠️ Features faltando para {ticker}: {len(features_disponiveis)}/{len(feature_cols)}")
                continue

            # Pegar os últimos 'sequence_length' dias para predição
            X_pred = dados_copy[feature_cols].tail(sequence_length)

            if len(X_pred) < sequence_length:
                continue  # Not enough data for sequence

            # Aplicar scaler das features
            X_scaled = scaler_features.transform(X_pred)
            X_scaled = X_scaled.reshape(1, sequence_length, len(feature_cols))  # Reshape for LSTM

            # Fazer predição da VARIAÇÃO PERCENTUAL
            variacao_pct_prevista_raw = model.predict(X_scaled)[0][0]

            # Desnormalizar a predição se o target foi normalizado
            if scaler_target is not None:
                variacao_pct_prevista = scaler_target.inverse_transform([[variacao_pct_prevista_raw]])[0][0]
            else:
                variacao_pct_prevista = variacao_pct_prevista_raw

            preco_atual = dados_copy['Media_OHLC'].iloc[-1]

            # Calcular preço previsto baseado na variação percentual
            if preco_atual > 0:
                preco_previsto = preco_atual * (1 + variacao_pct_prevista / 100)
            else:
                preco_previsto = 0.0

            # Gerar sinais baseados na variação percentual prevista
            if variacao_pct_prevista > threshold:
                sinal_compra = 1
                sinal_venda = 0
                sinal_tipo = "COMPRA"
            elif variacao_pct_prevista < -threshold:
                sinal_compra = 0
                sinal_venda = 1
                sinal_tipo = "VENDA"
            else:
                sinal_compra = 0
                sinal_venda = 0
                sinal_tipo = "SEM_SINAL"

            # Adicionar predições aos dados (apenas para o último dia)
            dados_copy.loc[dados_copy.index[-1], 'Preco_Previsto'] = preco_previsto
            dados_copy.loc[dados_copy.index[-1], 'Variacao_Pct_Prevista'] = variacao_pct_prevista
            dados_copy.loc[dados_copy.index[-1], 'Pred_Compra'] = sinal_compra
            dados_copy.loc[dados_copy.index[-1], 'Pred_Venda'] = sinal_venda
            dados_copy.loc[dados_copy.index[-1], 'Sinal_Tipo'] = sinal_tipo

            # Preencher NaN com valores padrão
            dados_copy['Preco_Previsto'] = dados_copy['Preco_Previsto'].fillna(0.0)
            dados_copy['Variacao_Pct_Prevista'] = dados_copy['Variacao_Pct_Prevista'].fillna(0.0)
            dados_copy['Pred_Compra'] = dados_copy['Pred_Compra'].fillna(0).astype(int)
            dados_copy['Pred_Venda'] = dados_copy['Pred_Venda'].fillna(0).astype(int)
            dados_copy['Sinal_Tipo'] = dados_copy['Sinal_Tipo'].fillna("SEM_SINAL")

            acoes_com_predicoes[ticker] = dados_copy

    print(f"   ✅ Predições aplicadas a {len(acoes_com_predicoes)} ações")
    print(f"   📊 Modelo prediz variação percentual da ação no dia seguinte")
    print(f"   📊 Threshold utilizado: {threshold}% de variação")

    return acoes_com_predicoes


def criar_graficos_lstm(y_test, y_pred, rmse, mae, r2):
    """
    Cria gráficos de análise do modelo LSTM para variações percentuais
    """
    print(f"\n📊 Criando gráficos de análise do modelo LSTM...")

    # Criar diretório de figuras
    figures_dir = 'results/figures/lstm_analysis'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files', True):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # Configurar estilo dos gráficos
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 10

    # 1. Gráfico: Valores Reais vs Preditos (Scatter Plot)
    fig, ax = plt.subplots(figsize=(10, 8))

    # Scatter plot
    ax.scatter(y_test, y_pred, alpha=0.6, color='steelblue', s=20)

    # Linha de referência perfeita (y = x)
    min_val = min(y_test.min(), y_pred.min())
    max_val = max(y_test.max(), y_pred.max())
    ax.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Predição Perfeita')

    # Configurações do gráfico
    ax.set_xlabel('Variação Real (%)', fontsize=12)
    ax.set_ylabel('Variação Predita (%)', fontsize=12)
    ax.set_title('LSTM Regressor: Variações Percentuais Reais vs Preditas\n' +
                f'R² = {r2:.4f} | RMSE = {rmse:.2f}% | MAE = {mae:.2f}%',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    # Adicionar estatísticas no gráfico
    ax.text(0.05, 0.95, f'Pontos: {len(y_test)}\nR²: {r2:.4f}\nRMSE: {rmse:.2f}%\nMAE: {mae:.2f}%',
            transform=ax.transAxes, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'valores_reais_vs_preditos.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. Gráfico: Distribuição dos Erros
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Calcular erros (agora em pontos percentuais)
    erros = y_pred - y_test

    # Histograma dos erros em pontos percentuais
    ax1.hist(erros, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax1.set_xlabel('Erro (pontos percentuais)', fontsize=12)
    ax1.set_ylabel('Frequência', fontsize=12)
    ax1.set_title('Distribuição dos Erros\n' +
                 f'Média: {erros.mean():.3f}pp | Desvio: {erros.std():.3f}pp',
                 fontsize=12, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Histograma dos erros absolutos
    erros_abs = np.abs(erros)
    ax2.hist(erros_abs, bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    ax2.set_xlabel('Erro Absoluto (pontos percentuais)', fontsize=12)
    ax2.set_ylabel('Frequência', fontsize=12)
    ax2.set_title('Distribuição dos Erros Absolutos\n' +
                 f'Média: {erros_abs.mean():.3f}pp | Desvio: {erros_abs.std():.3f}pp',
                 fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'distribuicao_erros.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 3. Gráfico: Q-Q Plot para normalidade dos resíduos
    fig, ax = plt.subplots(figsize=(8, 8))

    stats.probplot(erros, dist="norm", plot=ax)
    ax.set_title('Q-Q Plot: Normalidade dos Resíduos\n' +
                'Teste de Normalidade dos Erros do Modelo LSTM',
                fontsize=12, fontweight='bold')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'qq_plot_residuos.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 4. Gráfico: Série Temporal dos Últimos 200 Pontos
    fig, ax = plt.subplots(figsize=(14, 8))

    # Pegar últimos 200 pontos para visualização
    n_pontos = min(200, len(y_test))
    indices = range(len(y_test) - n_pontos, len(y_test))

    ax.plot(indices, y_test[-n_pontos:], 'o-', color='blue', linewidth=2,
            markersize=4, label='Variações Reais', alpha=0.8)
    ax.plot(indices, y_pred[-n_pontos:], 's-', color='red', linewidth=2,
            markersize=4, label='Variações Preditas', alpha=0.8)

    ax.set_xlabel('Índice da Amostra', fontsize=12)
    ax.set_ylabel('Variação (%)', fontsize=12)
    ax.set_title(f'LSTM Regressor: Série Temporal (Últimos {n_pontos} Pontos)\n' +
                f'Comparação entre Variações Reais e Preditas', fontsize=14, fontweight='bold')
    ax.legend(loc='upper left')
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'serie_temporal_predicoes.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 5. Gráfico: Resíduos vs Valores Preditos
    fig, ax = plt.subplots(figsize=(10, 8))

    ax.scatter(y_pred, erros, alpha=0.6, color='green', s=20)
    ax.axhline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax.set_xlabel('Variações Preditas (%)', fontsize=12)
    ax.set_ylabel('Resíduos (pontos percentuais)', fontsize=12)
    ax.set_title('Resíduos vs Variações Preditas\n' +
                'Análise de Homocedasticidade do Modelo LSTM',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'residuos_vs_preditos.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 6. Gráfico: Boxplot dos Erros por Faixa de Variação
    fig, ax = plt.subplots(figsize=(12, 8))

    # Criar faixas de variação
    faixas = pd.cut(y_test, bins=5, labels=['Muito Negativa', 'Negativa', 'Neutra', 'Positiva', 'Muito Positiva'])
    dados_boxplot = [erros[faixas == faixa] for faixa in faixas.categories]

    ax.boxplot(dados_boxplot, labels=faixas.categories)
    ax.axhline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax.set_xlabel('Faixa de Variação', fontsize=12)
    ax.set_ylabel('Erro (pontos percentuais)', fontsize=12)
    ax.set_title('Distribuição dos Erros por Faixa de Variação\n' +
                'Análise da Performance do Modelo por Segmento',
                fontsize=14, fontweight='bold')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(os.path.join(figures_dir, 'erros_por_faixa_preco.png'), dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ Gráficos salvos em: {figures_dir}")
    print(f"   📊 6 gráficos de análise criados:")
    print(f"   • valores_reais_vs_preditos.png")
    print(f"   • distribuicao_erros.png")
    print(f"   • qq_plot_residuos.png")
    print(f"   • serie_temporal_predicoes.png")
    print(f"   • residuos_vs_preditos.png")
    print(f"   • erros_por_faixa_preco.png")


def imprimir_recomendacoes_lstm(acoes_com_predicoes):
    """
    Imprime recomendações de compra e venda baseadas no LSTM
    Sinais de venda apenas para ações na carteira atual
    Baseado na predição de preços do modelo LSTM
    """
    print(f"\n📊 ESTRATÉGIA DE TRADING - REGRESSOR LSTM")
    print(f"=" * 60)

    # Verificar data mais recente disponível
    data_mais_recente = None
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultima_data = dados.index.max()
            if data_mais_recente is None or ultima_data > data_mais_recente:
                data_mais_recente = ultima_data

    if data_mais_recente:
        print(f"� Análise baseada nos dados até: {data_mais_recente.strftime('%d/%m/%Y (%A)')}")
        print(f"🤖 Sinais baseados na predição de preços do modelo LSTM (regressão)")
        print(f"🎯 Threshold: {config.get('xgboost.features.pct_threshold', 0.4)}% de variação")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Coletar sinais
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')
            ultimo_dia = dados.tail(1).iloc[0]

            # Verificar se há sinais
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker_clean': ticker_clean,
                    'nome': ticker_clean,  # Nome simplificado
                    'preco': ultimo_dia.get('Media_OHLC', 0),
                    'preco_previsto': ultimo_dia.get('Preco_Previsto', 0),
                    'variacao_pct_prevista': ultimo_dia.get('Variacao_Pct_Prevista', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'spread': ultimo_dia.get('Spread', 0),
                    'data': ultimo_dia.name
                })
            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas mostrar sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker_clean': ticker_clean,
                        'nome': ticker_clean,  # Nome simplificado
                        'preco': ultimo_dia.get('Media_OHLC', 0),
                        'preco_previsto': ultimo_dia.get('Preco_Previsto', 0),
                        'variacao_pct_prevista': ultimo_dia.get('Variacao_Pct_Prevista', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name,
                        'quantidade_carteira': carteira_atual[ticker]
                    })

    # Exibir sinais de venda PRIMEIRO (ordenados por maior queda prevista)
    # APENAS para ações na carteira atual
    if sinais_venda:
        print(f"\n🔴 SINAIS DE VENDA ({len(sinais_venda)} ações na carteira):")
        print("-" * 60)
        # Ordenar por maior queda prevista (mais negativo primeiro)
        for sinal in sorted(sinais_venda, key=lambda x: x['variacao_pct_prevista']):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            quantidade = sinal.get('quantidade_carteira', 0)
            print(f"   📉 {sinal['ticker_clean']} ({sinal['nome'][:30]}) - {quantidade} ações na carteira")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Preço previsto: R$ {sinal['preco_previsto']:.2f} ({sinal['variacao_pct_prevista']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 LSTM prevê queda no preço")
            print()
    else:
        print(f"\n🔴 SINAIS DE VENDA: Nenhuma ação na carteira com sinal de venda")
        print("-" * 60)

    # Exibir sinais de compra DEPOIS (ordenados por maior alta prevista)
    if sinais_compra:
        print(f"\n🟢 SINAIS DE COMPRA ({len(sinais_compra)} ações):")
        print("-" * 60)
        # Ordenar por maior alta prevista (mais positivo primeiro)
        for sinal in sorted(sinais_compra, key=lambda x: x['variacao_pct_prevista'], reverse=True):
            data_str = pd.to_datetime(sinal['data']).strftime('%d/%m/%Y') if pd.notna(sinal['data']) else 'N/A'
            print(f"   📈 {sinal['ticker_clean']} ({sinal['nome'][:30]})")
            print(f"      💰 Preço atual: R$ {sinal['preco']:.2f}")
            print(f"      🎯 Preço previsto: R$ {sinal['preco_previsto']:.2f} ({sinal['variacao_pct_prevista']:+.2f}%)")
            print(f"      📅 Data: {data_str}")
            print(f"      📊 Volume: {sinal['volume']:,.0f}")
            if sinal['volatilidade'] > 0:
                print(f"      📈 Volatilidade: {sinal['volatilidade']:.4f}")
            if sinal['spread'] > 0:
                print(f"      💹 Spread: {sinal['spread']:.4f}")
            print(f"      🤖 LSTM prevê alta no preço")
            print()

    # Resumo final
    print("📋 RESUMO DOS SINAIS:")
    print(f"   🔴 Venda: {len(sinais_venda)} ações na carteira (ordenadas por maior queda prevista)")
    print(f"   🟢 Compra: {len(sinais_compra)} ações (ordenadas por maior alta prevista)")
    print(f"   📊 Total de sinais: {len(sinais_compra) + len(sinais_venda)} ações")
    print(f"   🎯 Threshold: {config.get('xgboost.features.pct_threshold', 0.4)}% de variação")
    if carteira_atual:
        print(f"   📋 Carteira atual: {len(carteira_atual)} posições ativas")


def salvar_resultados_lstm(acoes_com_predicoes, metricas_modelo, feature_cols):
    """
    Salva os resultados do LSTM em arquivos CSV
    Sinais de venda apenas para ações na carteira atual
    Segue o mesmo formato do estimador XGBoost
    """
    setup_environment()

    # Criar diretório de saída
    output_dir = 'results/csv/lstm_analysis'
    individual_dir = os.path.join(output_dir, 'individual_stocks')
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(individual_dir, exist_ok=True)

    # Limpar arquivos antigos
    if config.get('output.clean_old_files', True):
        for arquivo in os.listdir(output_dir):
            if arquivo.endswith('.csv'):
                os.remove(os.path.join(output_dir, arquivo))
        for arquivo in os.listdir(individual_dir):
            if arquivo.endswith('.csv'):
                os.remove(os.path.join(individual_dir, arquivo))
        print(f"🗑️ Arquivos CSV antigos removidos de {output_dir}")

    # Carregar carteira atual para filtrar sinais de venda
    carteira_atual = carregar_carteira_atual()

    # Coletar dados para salvar
    dados_completos = []
    sinais_compra = []
    sinais_venda = []

    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ticker_clean = ticker.replace('.SA', '')

            # Preparar dados para CSV (formato igual ao XGBoost)
            dados_csv = dados.copy()
            dados_csv['Ticker'] = ticker_clean

            # Renomear colunas para consistência com XGBoost
            if 'Preco_Previsto' in dados_csv.columns:
                dados_csv['Valor_Estimado'] = dados_csv['Preco_Previsto']
            if 'Variacao_Pct_Prevista' in dados_csv.columns:
                dados_csv['Pct_Change_Estimado'] = dados_csv['Variacao_Pct_Prevista']

            # Resetar índice para ter coluna Date
            dados_csv = dados_csv.reset_index()

            # Encontrar a coluna de data (pode ser 'Date', 'index', ou outro nome)
            coluna_data = None
            for col in dados_csv.columns:
                if dados_csv[col].dtype.name.startswith('datetime'):
                    coluna_data = col
                    break

            if coluna_data:
                dados_csv['Data'] = dados_csv[coluna_data].dt.strftime('%Y-%m-%d')
                dados_csv.drop(coluna_data, axis=1, inplace=True)
            else:
                # Se não encontrou coluna de data, usar o índice original
                dados_csv['Data'] = dados.index.strftime('%Y-%m-%d')

            # Selecionar colunas principais (igual ao XGBoost)
            colunas_principais = ['Ticker', 'Data', 'Media_OHLC', 'Volume', 'Spread', 'Volatilidade',
                                'Valor_Estimado', 'Pct_Change_Estimado', 'Pred_Compra', 'Pred_Venda', 'Sinal_Tipo']
            colunas_existentes = [col for col in colunas_principais if col in dados_csv.columns]

            if len(colunas_existentes) >= 6:  # Pelo menos as colunas principais
                dados_selecionados = dados_csv[colunas_existentes].copy()

                # Preencher valores NaN
                dados_selecionados['Valor_Estimado'] = dados_selecionados['Valor_Estimado'].fillna(0.0)
                dados_selecionados['Pct_Change_Estimado'] = dados_selecionados['Pct_Change_Estimado'].fillna(0.0)
                dados_selecionados['Pred_Compra'] = dados_selecionados['Pred_Compra'].fillna(0).astype(int)
                dados_selecionados['Pred_Venda'] = dados_selecionados['Pred_Venda'].fillna(0).astype(int)
                dados_selecionados['Sinal_Tipo'] = dados_selecionados['Sinal_Tipo'].fillna("SEM_SINAL")

                dados_completos.append(dados_selecionados)

                # Salvar arquivo individual
                arquivo_individual = os.path.join(individual_dir, f'lstm_{ticker_clean}.csv')
                dados_selecionados.to_csv(arquivo_individual, index=False)

            # Coletar sinais do último dia
            ultimo_dia = dados.tail(1).iloc[0]
            if ultimo_dia.get('Pred_Compra', 0) == 1:
                sinais_compra.append({
                    'ticker': ticker_clean,
                    'nome': ticker_clean,
                    'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                    'valor_estimado': ultimo_dia.get('Preco_Previsto', 0),
                    'pct_change_estimado': ultimo_dia.get('Variacao_Pct_Prevista', 0),
                    'volume': ultimo_dia.get('Volume', 0),
                    'volatilidade': ultimo_dia.get('Volatilidade', 0),
                    'spread': ultimo_dia.get('Spread', 0),
                    'data': ultimo_dia.name.strftime('%Y-%m-%d') if pd.notna(ultimo_dia.name) else 'N/A'
                })
            elif ultimo_dia.get('Pred_Venda', 0) == 1:
                # FILTRO: Apenas sinais de venda para ações na carteira
                if ticker in carteira_atual and carteira_atual[ticker] > 0:
                    sinais_venda.append({
                        'ticker': ticker_clean,
                        'nome': ticker_clean,
                        'preco_atual': ultimo_dia.get('Media_OHLC', 0),
                        'valor_estimado': ultimo_dia.get('Preco_Previsto', 0),
                        'pct_change_estimado': ultimo_dia.get('Variacao_Pct_Prevista', 0),
                        'volume': ultimo_dia.get('Volume', 0),
                        'volatilidade': ultimo_dia.get('Volatilidade', 0),
                        'spread': ultimo_dia.get('Spread', 0),
                        'data': ultimo_dia.name.strftime('%Y-%m-%d') if pd.notna(ultimo_dia.name) else 'N/A',
                        'quantidade_carteira': carteira_atual[ticker]
                    })

    # Combinar todos os dados
    if dados_completos:
        df_final = pd.concat(dados_completos, ignore_index=True)
        df_final = df_final.sort_values(['Ticker', 'Data'])

        # Salvar arquivo completo principal
        csv_completo_path = os.path.join(output_dir, 'resultados_lstm_completo.csv')
        df_final.to_csv(csv_completo_path, index=False)

    # Salvar resumo de sinais de venda (ordenados por maior queda estimada)
    if sinais_venda:
        df_venda = pd.DataFrame(sinais_venda)
        df_venda = df_venda.sort_values('pct_change_estimado', ascending=True)  # Maior queda primeiro
        arquivo_venda = os.path.join(output_dir, 'sinais_venda_lstm.csv')
        df_venda.to_csv(arquivo_venda, index=False)

    # Salvar resumo de sinais de compra (ordenados por maior alta estimada)
    if sinais_compra:
        df_compra = pd.DataFrame(sinais_compra)
        df_compra = df_compra.sort_values('pct_change_estimado', ascending=False)  # Maior alta primeiro
        arquivo_compra = os.path.join(output_dir, 'sinais_compra_lstm.csv')
        df_compra.to_csv(arquivo_compra, index=False)

    # Salvar métricas do modelo
    metricas_df = pd.DataFrame([{
        'metrica': 'RMSE',
        'valor': metricas_modelo['rmse'],
        'unidade': 'pontos_percentuais'
    }, {
        'metrica': 'MAE',
        'valor': metricas_modelo['mae'],
        'unidade': 'pontos_percentuais'
    }, {
        'metrica': 'R2',
        'valor': metricas_modelo['r2'],
        'unidade': 'coeficiente'
    }])
    metricas_path = os.path.join(output_dir, 'metricas_lstm.csv')
    metricas_df.to_csv(metricas_path, index=False)

    print(f"💾 Resultados salvos em: {output_dir}")
    print(f"   📊 Arquivo completo: resultados_lstm_completo.csv ({len(df_final) if dados_completos else 0} registros)")
    print(f"   🟢 Sinais de compra: sinais_compra_lstm.csv ({len(sinais_compra)} sinais)")
    print(f"   🔴 Sinais de venda: sinais_venda_lstm.csv ({len(sinais_venda)} sinais)")
    print(f"   📈 Métricas: metricas_lstm.csv")
    print(f"   📁 Arquivos individuais: individual_stocks/ ({len(dados_completos)} ações)")


def criar_graficos_sinais_individuais_lstm(acoes_com_predicoes, max_acoes=10):
    """
    Cria gráficos individuais para ações com sinais do LSTM
    Baseado na função equivalente do XGBoost
    """
    setup_environment()

    # Criar diretório de figuras
    figures_dir = 'results/figures/lstm_analysis/individual_stocks'
    os.makedirs(figures_dir, exist_ok=True)

    # Limpar figuras antigas
    if config.get('output.clean_old_files', True):
        for arquivo in os.listdir(figures_dir):
            if arquivo.endswith('.png'):
                os.remove(os.path.join(figures_dir, arquivo))
        print(f"🗑️ Figuras antigas removidas de {figures_dir}")

    # Coletar ações com sinais
    acoes_com_sinais = []
    for ticker, dados in acoes_com_predicoes.items():
        if dados is not None and len(dados) > 0:
            ultimo_dia = dados.tail(1).iloc[0]
            if ultimo_dia.get('Pred_Compra', 0) == 1 or ultimo_dia.get('Pred_Venda', 0) == 1:
                acoes_com_sinais.append((ticker, dados))

    # Limitar número de gráficos
    acoes_selecionadas = acoes_com_sinais[:max_acoes]

    if not acoes_selecionadas:
        print(f"   ⚠️ Nenhuma ação com sinais encontrada")
        return

    print(f"   📊 Criando gráficos para {len(acoes_selecionadas)} ações com sinais...")

    # Configurar estilo dos gráficos
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (14, 10)
    plt.rcParams['font.size'] = 10

    for ticker, dados in acoes_selecionadas:
        ticker_clean = ticker.replace('.SA', '')

        try:
            # Pegar últimos 60 dias para visualização
            dados_plot = dados.tail(60).copy()

            if len(dados_plot) < 10:
                continue

            # Criar figura com subplots
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12),
                                               gridspec_kw={'height_ratios': [3, 1, 1]})

            # Subplot 1: Preço e Predições
            ax1.plot(dados_plot.index, dados_plot['Media_OHLC'],
                    'b-', linewidth=2, label='Preço Real', alpha=0.8)

            # Mostrar predições onde existem
            mask_pred = dados_plot['Preco_Previsto'].notna() & (dados_plot['Preco_Previsto'] > 0)
            if mask_pred.any():
                ax1.plot(dados_plot.index[mask_pred], dados_plot['Preco_Previsto'][mask_pred],
                        'r--', linewidth=2, label='Preço Previsto LSTM', alpha=0.8)

            # Marcar sinais de compra e venda
            sinais_compra = dados_plot[dados_plot['Pred_Compra'] == 1]
            sinais_venda = dados_plot[dados_plot['Pred_Venda'] == 1]

            if len(sinais_compra) > 0:
                ax1.scatter(sinais_compra.index, sinais_compra['Media_OHLC'],
                           color='green', marker='^', s=100, label='Sinal Compra', zorder=5)

            if len(sinais_venda) > 0:
                ax1.scatter(sinais_venda.index, sinais_venda['Media_OHLC'],
                           color='red', marker='v', s=100, label='Sinal Venda', zorder=5)

            ax1.set_title(f'{ticker_clean} - Sinais LSTM (Últimos 60 dias)',
                         fontsize=14, fontweight='bold')
            ax1.set_ylabel('Preço (R$)', fontsize=12)
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)

            # Subplot 2: Volume
            ax2.bar(dados_plot.index, dados_plot['Volume'],
                   color='lightblue', alpha=0.7, width=0.8)
            ax2.set_ylabel('Volume', fontsize=12)
            ax2.grid(True, alpha=0.3)

            # Subplot 3: Variação Percentual Prevista
            if 'Variacao_Pct_Prevista' in dados_plot.columns:
                mask_var = dados_plot['Variacao_Pct_Prevista'].notna()
                if mask_var.any():
                    cores = ['green' if x > 0 else 'red' for x in dados_plot['Variacao_Pct_Prevista'][mask_var]]
                    ax3.bar(dados_plot.index[mask_var], dados_plot['Variacao_Pct_Prevista'][mask_var],
                           color=cores, alpha=0.7, width=0.8)
                    ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
                    ax3.set_ylabel('Variação Prevista (%)', fontsize=12)
                    ax3.grid(True, alpha=0.3)

            # Ajustar layout e salvar
            plt.tight_layout()

            # Salvar figura
            filename = f'lstm_sinais_{ticker_clean}.png'
            filepath = os.path.join(figures_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()

        except Exception as e:
            print(f"   ⚠️ Erro ao criar gráfico para {ticker}: {e}")
            plt.close()

    print(f"✅ Gráficos individuais salvos em: {figures_dir}")
    print(f"   📊 {len(acoes_selecionadas)} gráficos criados")


def main():
    setup_environment()

    print("🤖 REGRESSOR LSTM - PREDIÇÃO DE PREÇOS")
    print("=" * 80)
    print("📊 Baseado nas mesmas features do classificador XGBoost")
    print("🎯 Modelo de regressão: prediz preço da ação no dia seguinte")

    signal_horizon = config.get('xgboost.signal_horizon')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    data_period = config.get('xgboost.data_period')

    print(f"🎯 Sinais: Compra/Venda baseados em {signal_horizon} dias à frente")
    print(f"🔧 Features básicas: pct_change da Média OHLC passada ({ohlc_lags} dias), Volume, Spread, Volatilidade, dia da semana, mês")
    print(f"🔬 Features econométricas: Volatilidade de Parkinson, MFI, EMV, Amihud, Roll Spread,")
    print(f"   Hurst, Volume, CMF, A/D Line, Volume Oscillator")
    print(f"📅 Período de dados: {data_period}")
    print("=" * 80)

    # Carregar lista de ações diversificadas
    acoes = carregar_acoes_diversificadas()

    print(f"\n📥 Carregando dados de {len(acoes)} ações usando cache...")
    acoes_dados = {}
    for ticker, nome in acoes:
        print(f"   • {ticker} ({nome})")

        # Usar cache otimizado se disponível, senão usar método tradicional
        dados = baixar_dados_acao_otimizado(ticker, nome, forcar_download=False)

        if dados is None or len(dados) == 0:
            print(f"     ❌ Falha ao carregar dados de {ticker}")
            continue

        # Os dados já vêm com features calculadas do cache otimizado
        # Se não há features, calcular aqui
        if 'Media_OHLC' not in dados.columns:
            dados = calcular_features_e_sinais(dados, ticker=ticker)
        print(f"     ➡️ Registros após cálculo de features: {len(dados)}")
        if len(dados) == 0:
            print(f"     ⚠️ Nenhum registro válido após cálculo de features para {ticker}")
        acoes_dados[ticker] = dados

    print(f"✅ Processadas {len(acoes_dados)} ações com sucesso")

    # Unificar todos os dados em um único DataFrame
    print(f"\n🔧 Preparando dataset combinado para treinamento...")
    todos_dfs = []
    for ticker, dados in acoes_dados.items():
        if dados is not None and len(dados) > 0:
            dados = dados.copy()
            dados['Ticker'] = ticker
            # Resetar índice para preservar as datas como coluna
            dados = dados.reset_index()
            todos_dfs.append(dados)
    if not todos_dfs:
        print("❌ Nenhum dado disponível para treinamento.")
        return
    dataset = pd.concat(todos_dfs, ignore_index=True)

    # Converter coluna Date para datetime se necessário
    if 'Date' in dataset.columns:
        dataset['Date'] = pd.to_datetime(dataset['Date'])
    elif dataset.index.name == 'Date' or isinstance(dataset.index, pd.DatetimeIndex):
        dataset = dataset.reset_index()
        dataset['Date'] = pd.to_datetime(dataset['Date'])

    # Calcular target como variação percentual (igual ao XGBoost)
    dataset = dataset.sort_values(['Ticker', 'Date'])
    dataset['Media_OHLC_Anterior'] = dataset.groupby('Ticker')['Media_OHLC'].shift(1)

    # Calcular variação percentual
    mask_valido = (dataset['Media_OHLC_Anterior'] > 0) & (~dataset['Media_OHLC_Anterior'].isna())
    dataset['Target_PctChange'] = np.nan
    dataset.loc[mask_valido, 'Target_PctChange'] = (
        (dataset.loc[mask_valido, 'Media_OHLC'] - dataset.loc[mask_valido, 'Media_OHLC_Anterior']) /
        dataset.loc[mask_valido, 'Media_OHLC_Anterior'] * 100
    )

    # Selecionar features e target
    ohlc_lags = config.get('xgboost.features.ohlc_lags')
    econometric_lags = config.get('xgboost.features.econometric_lags')
    weekday_features = ['Segunda', 'Terca', 'Quarta', 'Quinta', 'Sexta']
    month_features = [f'Mes_{i}' for i in range(1, 13)]
    quarter_features = ['Quarter_1', 'Quarter_2', 'Quarter_3', 'Quarter_4', 'Last_Day_Quarter']
    holiday_features = ['Pre_Feriado_Brasil']
    basic_features = [f'Media_OHLC_PctChange_Lag_{i}' for i in range(1, ohlc_lags + 1)] + weekday_features + month_features + quarter_features + holiday_features
    econometric_features_all = [
        'Volume', 'Spread', 'Volatilidade',
        'Parkinson_Volatility', 'MFI', 'EMV', 'EMV_MA',
        'Amihud', 'Roll_Spread', 'Hurst', 'Vol_per_Volume', 'CMF', 'AD_Line', 'VO',
        'High_Max_50', 'Low_Min_50'
    ]
    econometric_features_lagged = []
    for feature in econometric_features_all:
        for i in range(1, econometric_lags + 1):
            econometric_features_lagged.append(f'{feature}_Lag_{i}')
    feature_cols = basic_features + econometric_features_lagged
    feature_cols = [col for col in feature_cols if col in dataset.columns]
    target_col = 'Target_PctChange'  # Predizer variação percentual

    print(f"📊 Features selecionadas: {len(feature_cols)}")
    print(f"📊 Target: {target_col} (variação percentual)")

    # Remover NaN
    dataset_original = dataset.copy()
    dataset = dataset.dropna(subset=feature_cols + [target_col])
    print(f"📊 Registros após remoção de NaN: {len(dataset)} (removidos: {len(dataset_original) - len(dataset)})")

    # Divisão temporal (80% treino, 20% teste)
    test_size = config.get('xgboost.test_size', 0.2)
    split_idx = int(len(dataset) * (1 - test_size))
    train_df = dataset.iloc[:split_idx].copy()
    test_df = dataset.iloc[split_idx:].copy()

    # Normalizar features (CORREÇÃO: fit apenas no treino)
    scaler_features = StandardScaler()
    train_df[feature_cols] = scaler_features.fit_transform(train_df[feature_cols])
    test_df[feature_cols] = scaler_features.transform(test_df[feature_cols])

    # Normalizar target se configurado
    normalize_target = config.get('lstm.normalize_target', True)
    if normalize_target:
        scaler_target = StandardScaler()
        train_df[[target_col]] = scaler_target.fit_transform(train_df[[target_col]])
        test_df[[target_col]] = scaler_target.transform(test_df[[target_col]])
        print(f"📊 Normalização aplicada:")
        print(f"   • Features: StandardScaler (média=0, std=1)")
        print(f"   • Target: StandardScaler (média=0, std=1)")
    else:
        scaler_target = None
        print(f"📊 Normalização aplicada:")
        print(f"   • Features: StandardScaler (média=0, std=1)")
        print(f"   • Target: Sem normalização (valores originais)")

    # Resetar índices para a preparação de sequências
    train_df = train_df.reset_index(drop=True)
    test_df = test_df.reset_index(drop=True)

    # Preparar dados para LSTM
    sequence_length = config.get('lstm.sequence_length', 20)
    X_train, y_train = preparar_dados_lstm(train_df, feature_cols, target_col, sequence_length)
    X_test, y_test = preparar_dados_lstm(test_df, feature_cols, target_col, sequence_length)

    print(f"\n📊 Dados para LSTM:")
    print(f"   • Treino: {X_train.shape[0]} sequências")
    print(f"   • Teste: {X_test.shape[0]} sequências")
    print(f"   • Features: {len(feature_cols)}")
    print(f"   • Sequence length: {sequence_length}")

    # Estatísticas do target (preços)
    print(f"\nEstatísticas do target (preços) no treino:")
    print(f"   • Preço mínimo: R$ {y_train.min():.2f}")
    print(f"   • Preço máximo: R$ {y_train.max():.2f}")
    print(f"   • Preço médio: R$ {y_train.mean():.2f}")
    print(f"   • Desvio padrão: R$ {y_train.std():.2f}")

    # Verificar se modelo LSTM já foi treinado hoje (considerando force_training)
    modelo_dir = 'results/models/lstm_analysis'
    modelo_nome = 'modelo_lstm.h5'
    os.makedirs(modelo_dir, exist_ok=True)

    force_training = config.get('xgboost', {}).get('cache', {}).get('force_training', False)
    if force_training:
        print(f"🔄 Forçando treinamento do modelo LSTM (force_training=True)")
        modelo_treinado_hoje, modelo_path = False, None
    else:
        modelo_treinado_hoje, modelo_path = verificar_modelo_lstm_treinado_hoje(modelo_dir, modelo_nome)

    if modelo_treinado_hoje:
        # Carregar modelo existente
        print(f"\n📦 Carregando modelo LSTM treinado hoje...")
        scaler_features_path = os.path.join(modelo_dir, 'scaler_features_lstm.pkl')
        scaler_target_path = os.path.join(modelo_dir, 'scaler_target_lstm.pkl')
        model, scaler_features_carregado, scaler_target_carregado = carregar_modelo_lstm_existente(
            modelo_path, scaler_features_path, scaler_target_path)

        if model is None:
            print("❌ Erro ao carregar modelo LSTM existente, retreinando...")
            modelo_treinado_hoje = False
        else:
            # Usar scalers carregados
            scaler_features = scaler_features_carregado
            scaler_target = scaler_target_carregado
            print(f"✅ Modelo LSTM carregado com sucesso!")

            # Avaliar modelo carregado
            print("\n📊 Avaliando modelo carregado...")
            y_pred_raw = model.predict(X_test).flatten()

            # Desnormalizar predições se necessário
            if scaler_target is not None:
                y_pred = scaler_target.inverse_transform(y_pred_raw.reshape(-1, 1)).flatten()
                y_test_denormalized = scaler_target.inverse_transform(y_test.reshape(-1, 1)).flatten()
            else:
                y_pred = y_pred_raw
                y_test_denormalized = y_test

            # Calcular métricas de regressão (com dados desnormalizados)
            mse = mean_squared_error(y_test_denormalized, y_pred)
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(y_test_denormalized, y_pred)
            r2 = r2_score(y_test_denormalized, y_pred)

    if not modelo_treinado_hoje:
        # Treinar modelo LSTM
        print("\n🚀 Treinando modelo LSTM para regressão...")
        model, _ = treinar_lstm_regressao(
            X_train, y_train, X_test, y_test, input_shape=(sequence_length, len(feature_cols)),
            epochs=config.get('lstm.epochs', 50),
            batch_size=config.get('lstm.batch_size', 32)
        )

        # Avaliar modelo
        print("\n📊 Avaliando modelo...")
        y_pred_raw = model.predict(X_test).flatten()

        # Desnormalizar predições se necessário
        if scaler_target is not None:
            y_pred = scaler_target.inverse_transform(y_pred_raw.reshape(-1, 1)).flatten()
            y_test_denormalized = scaler_target.inverse_transform(y_test.reshape(-1, 1)).flatten()
        else:
            y_pred = y_pred_raw
            y_test_denormalized = y_test

        # Calcular métricas de regressão (com dados desnormalizados)
        mse = mean_squared_error(y_test_denormalized, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test_denormalized, y_pred)
        r2 = r2_score(y_test_denormalized, y_pred)

        # Salvar modelo treinado
        print(f"\n💾 Salvando modelo LSTM...")
        metricas = {'rmse': rmse, 'mae': mae, 'r2': r2}
        salvar_modelo_lstm(model, scaler_features, scaler_target, feature_cols, metricas)

    print(f"\n🎯 Métricas de regressão no conjunto de teste:")
    print(f"   • RMSE: {rmse:.4f}%")
    print(f"   • MAE: {mae:.4f}%")
    print(f"   • R²: {r2:.4f}")

    # Estatísticas das variações percentuais no teste (desnormalizadas)
    print(f"\nEstatísticas das variações percentuais no teste:")
    print(f"   • Variação real mínima: {y_test_denormalized.min():.2f}%")
    print(f"   • Variação real máxima: {y_test_denormalized.max():.2f}%")
    print(f"   • Variação real média: {y_test_denormalized.mean():.2f}%")
    print(f"   • Variação prevista mínima: {y_pred.min():.2f}%")
    print(f"   • Variação prevista máxima: {y_pred.max():.2f}%")
    print(f"   • Variação prevista média: {y_pred.mean():.2f}%")

    # Criar gráficos de análise (com dados desnormalizados)
    criar_graficos_lstm(y_test_denormalized, y_pred, rmse, mae, r2)

    # Aplicar predições aos dados das ações
    print("\n🔮 Aplicando predições aos dados das ações...")
    acoes_com_predicoes = aplicar_predicoes_lstm(acoes_dados, model, scaler_features, scaler_target, feature_cols, sequence_length)

    # Imprimir recomendações
    imprimir_recomendacoes_lstm(acoes_com_predicoes)

    # Salvar resultados em CSV (igual ao XGBoost)
    print(f"\n💾 Salvando resultados...")
    salvar_resultados_lstm(acoes_com_predicoes, {'rmse': rmse, 'mae': mae, 'r2': r2}, feature_cols)

    # Criar gráficos individuais de sinais (igual ao XGBoost)
    print(f"\n📊 Criando gráficos individuais de sinais...")
    criar_graficos_sinais_individuais_lstm(acoes_com_predicoes)

    print(f"\n✅ Análise LSTM concluída!")
    print(f"📊 Modelo treinado com {len(feature_cols)} features")
    print(f"🎯 R² final: {r2:.3f}")
    print(f"📊 RMSE final: {rmse:.4f}%")


if __name__ == "__main__":
    main()
